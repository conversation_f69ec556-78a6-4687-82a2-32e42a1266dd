from flask import Blueprint, request, jsonify, current_app, url_for
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from marshmallow import ValidationError
from sqlalchemy import or_, and_, desc, asc
import json
import os

from app import db
from app.models import House, User
from app.schemas import house_schema, houses_schema, HouseSearchSchema
from app.utils.helpers import allowed_file, save_file_locally, upload_file_to_s3, paginate_query
from app.utils.decorators import role_required, owner_or_admin_required

# 创建蓝图
bp = Blueprint('houses', __name__)

@bp.route('', methods=['POST'])
@jwt_required()
@role_required('landlord', 'admin')
def create_house():
    """发布新房源"""
    try:
        # 获取并验证数据
        data = request.get_json() or {}
        if not data:
            return jsonify({"error": "数据不能为空"}), 400

        # 设置房东ID为当前用户
        current_user_id = get_jwt_identity()
        data['landlord_id'] = current_user_id

        # 使用schema验证数据
        house_data = house_schema.load(data)

        # 创建房源
        house = House(
            landlord_id=current_user_id,
            address=house_data['address'],
            city=house_data.get('city', ''),
            district=house_data.get('district', ''),
            community_name=house_data.get('community_name', ''),
            house_type=house_data.get('house_type', ''),
            layout=house_data.get('layout', ''),
            area=house_data['area'],
            rent_price=house_data['rent_price'],
            deposit_price=house_data['deposit_price'],
            decoration_status=house_data.get('decoration_status', ''),
            description=house_data.get('description', ''),
            video_url=house_data.get('video_url', ''),
            status="空置",
            latitude=house_data.get('latitude'),
            longitude=house_data.get('longitude'),
            is_verified_by_admin=False
        )

        # 处理图片列表
        if 'photos' in house_data and house_data['photos']:
            house.set_photos_list(house_data['photos'])

        db.session.add(house)
        db.session.commit()

        return jsonify({
            "message": "房源发布成功",
            "house": house_schema.dump(house)
        }), 201

    except ValidationError as err:
        return jsonify({"error": "验证失败", "errors": err.messages}), 422
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"发布房源失败: {str(e)}")
        return jsonify({"error": "发布房源失败", "message": str(e)}), 500

@bp.route('/<int:house_id>', methods=['GET'])
def get_house(house_id):
    """获取房源详情"""
    try:
        house = House.query.get_or_404(house_id)

        # 检查是否包含房东信息
        include_landlord = request.args.get('include_landlord') == 'true'

        return jsonify({
            "house": house_schema.dump(house)
        }), 200

    except Exception as e:
        current_app.logger.error(f"获取房源详情失败: {str(e)}")
        return jsonify({"error": "获取房源详情失败", "message": str(e)}), 500

@bp.route('', methods=['GET'])
def get_houses():
    """获取房源列表（支持搜索和筛选）"""
    try:
        schema = HouseSearchSchema()
        search_params = schema.load(request.args)

        # 构建查询
        query = House.query

        # 基本筛选条件
        if 'city' in search_params and search_params['city']:
            query = query.filter(House.city == search_params['city'])
        if 'district' in search_params and search_params['district']:
            query = query.filter(House.district == search_params['district'])
        if 'community_name' in search_params and search_params['community_name']:
            query = query.filter(House.community_name == search_params['community_name'])
        if 'house_type' in search_params and search_params['house_type']:
            query = query.filter(House.house_type == search_params['house_type'])
        if 'layout' in search_params and search_params['layout']:
            query = query.filter(House.layout == search_params['layout'])

        # 价格范围
        if 'min_price' in search_params:
            query = query.filter(House.rent_price >= search_params['min_price'])
        if 'max_price' in search_params:
            query = query.filter(House.rent_price <= search_params['max_price'])

        # 面积范围
        if 'min_area' in search_params:
            query = query.filter(House.area >= search_params['min_area'])
        if 'max_area' in search_params:
            query = query.filter(House.area <= search_params['max_area'])

        # 关键词搜索（地址、描述、社区名）
        if 'keywords' in search_params and search_params['keywords']:
            keywords = search_params['keywords']
            query = query.filter(or_(
                House.address.ilike(f'%{keywords}%'),
                House.description.ilike(f'%{keywords}%'),
                House.community_name.ilike(f'%{keywords}%'),
                House.house_type.ilike(f'%{keywords}%'),
                House.layout.ilike(f'%{keywords}%')
            ))

        # 状态筛选（默认只显示"空置"状态的房源）
        if 'status' in search_params:
            query = query.filter(House.status == search_params['status'])
        else:
            # 游客只能查看"空置"和"已验证"的房源
            if not request.headers.get('Authorization'):
                query = query.filter(and_(
                    House.status == "空置",
                    House.is_verified_by_admin == True
                ))

        # 排序
        if 'sort_by' in search_params:
            sort = search_params['sort_by']
            if sort == 'price_asc':
                query = query.order_by(asc(House.rent_price))
            elif sort == 'price_desc':
                query = query.order_by(desc(House.rent_price))
            elif sort == 'area_asc':
                query = query.order_by(asc(House.area))
            elif sort == 'area_desc':
                query = query.order_by(desc(House.area))
            elif sort == 'date_asc':
                query = query.order_by(asc(House.created_at))
            elif sort == 'date_desc':
                query = query.order_by(desc(House.created_at))
        else:
            # 默认按创建时间倒序
            query = query.order_by(desc(House.created_at))

        # 分页
        page = search_params.get('page', 1)
        per_page = search_params.get('per_page', 20)

        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            "houses": houses_schema.dump(pagination.items),
            "total": pagination.total,
            "pages": pagination.pages,
            "page": pagination.page,
            "per_page": pagination.per_page
        }), 200

    except ValidationError as err:
        return jsonify({"error": "验证失败", "errors": err.messages}), 422
    except Exception as e:
        current_app.logger.error(f"获取房源列表失败: {str(e)}")
        return jsonify({"error": "获取房源列表失败", "message": str(e)}), 500

@bp.route('/<int:house_id>', methods=['PUT'])
@jwt_required()
@owner_or_admin_required(House, 'house_id')
def update_house(house_id):
    """更新房源信息"""
    try:
        house = House.query.get_or_404(house_id)

        data = request.get_json() or {}
        if not data:
            return jsonify({"error": "数据不能为空"}), 400

        # 禁止修改的字段
        disallowed_fields = ['landlord_id', 'created_at', 'updated_at']
        for field in disallowed_fields:
            if field in data:
                return jsonify({"error": f"不允许修改字段: {field}"}), 400

        # 检查当前用户角色
        claims = get_jwt()
        user_role = claims.get('role')

        # 只有管理员可以修改验证状态
        if 'is_verified_by_admin' in data and user_role != 'admin':
            return jsonify({"error": "只有管理员可以修改房源验证状态"}), 403

        # 使用schema验证数据
        house_data = house_schema.load(data, partial=True)

        # 更新可编辑字段
        editable_fields = [
            'address', 'city', 'district', 'community_name', 'house_type',
            'layout', 'area', 'rent_price', 'deposit_price', 'decoration_status',
            'description', 'video_url', 'status', 'latitude', 'longitude'
        ]

        if user_role == 'admin':
            editable_fields.append('is_verified_by_admin')

        for field in editable_fields:
            if field in house_data:
                setattr(house, field, house_data[field])

        # 处理图片列表
        if 'photos' in house_data:
            house.set_photos_list(house_data['photos'])

        db.session.commit()

        return jsonify({
            "message": "房源信息更新成功",
            "house": house_schema.dump(house)
        }), 200

    except ValidationError as err:
        return jsonify({"error": "验证失败", "errors": err.messages}), 422
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新房源信息失败: {str(e)}")
        return jsonify({"error": "更新房源信息失败", "message": str(e)}), 500

@bp.route('/<int:house_id>', methods=['DELETE'])
@jwt_required()
@owner_or_admin_required(House, 'house_id')
def delete_house(house_id):
    """删除房源"""
    try:
        house = House.query.get_or_404(house_id)

        # 如果有关联的租约，不允许删除
        if house.leases.count() > 0:
            return jsonify({"error": "该房源有关联的租约，不能删除"}), 400

        # 删除房源
        db.session.delete(house)
        db.session.commit()

        return jsonify({
            "message": "房源删除成功"
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除房源失败: {str(e)}")
        return jsonify({"error": "删除房源失败", "message": str(e)}), 500

@bp.route('/<int:house_id>/upload_photo', methods=['POST'])
@jwt_required()
@owner_or_admin_required(House, 'house_id')
def upload_house_photo(house_id):
    """上传房源照片"""
    try:
        house = House.query.get_or_404(house_id)

        # 检查是否有文件上传
        if 'photo' not in request.files:
            return jsonify({"error": "没有上传文件"}), 400

        file = request.files['photo']
        if file.filename == '':
            return jsonify({"error": "未选择文件"}), 400

        if not allowed_file(file.filename, {'png', 'jpg', 'jpeg', 'gif'}):
            return jsonify({"error": "不支持的文件类型"}), 400

        # 处理文件上传（本地存储或云存储）
        if current_app.config.get('AWS_ACCESS_KEY_ID'):
            # 使用S3存储
            photo_url = upload_file_to_s3(file, f'houses/{house_id}/')
            if not photo_url:
                return jsonify({"error": "上传照片失败"}), 500
        else:
            # 使用本地存储
            house_photos_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], f'houses/{house_id}')
            filename = save_file_locally(file, house_photos_dir)
            photo_url = url_for('static', filename=f'uploads/houses/{house_id}/{filename}', _external=True)

        # 更新房源照片列表
        photos_list = house.get_photos_list()
        photos_list.append(photo_url)
        house.set_photos_list(photos_list)

        db.session.commit()

        return jsonify({
            "message": "照片上传成功",
            "photo_url": photo_url,
            "photos": house.get_photos_list()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"上传照片失败: {str(e)}")
        return jsonify({"error": "上传照片失败", "message": str(e)}), 500

@bp.route('/<int:house_id>/photos/<int:photo_index>', methods=['DELETE'])
@jwt_required()
@owner_or_admin_required(House, 'house_id')
def delete_house_photo(house_id, photo_index):
    """删除房源照片"""
    try:
        house = House.query.get_or_404(house_id)

        photos_list = house.get_photos_list()
        if not photos_list or photo_index >= len(photos_list) or photo_index < 0:
            return jsonify({"error": "照片索引无效"}), 404

        # 删除照片
        del photos_list[photo_index]
        house.set_photos_list(photos_list)

        db.session.commit()

        return jsonify({
            "message": "照片删除成功",
            "photos": house.get_photos_list()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除照片失败: {str(e)}")
        return jsonify({"error": "删除照片失败", "message": str(e)}), 500

@bp.route('/<int:house_id>/status', methods=['PATCH'])
@jwt_required()
@owner_or_admin_required(House, 'house_id')
def update_house_status(house_id):
    """更新房源状态"""
    try:
        house = House.query.get_or_404(house_id)

        data = request.get_json() or {}
        if 'status' not in data:
            return jsonify({"error": "请提供要更新的状态"}), 400

        # 验证状态值
        valid_statuses = ["空置", "出租中", "维修中", "已下架"]
        if data['status'] not in valid_statuses:
            return jsonify({"error": f"无效的状态值，必须是以下之一: {', '.join(valid_statuses)}"}), 400

        # 更新状态
        house.status = data['status']
        db.session.commit()

        return jsonify({
            "message": "房源状态更新成功",
            "house": house_schema.dump(house)
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新房源状态失败: {str(e)}")
        return jsonify({"error": "更新房源状态失败", "message": str(e)}), 500

@bp.route('/<int:house_id>/verify', methods=['PATCH'])
@jwt_required()
@role_required('admin')
def verify_house(house_id):
    """管理员验证房源"""
    try:
        house = House.query.get_or_404(house_id)

        data = request.get_json() or {}
        if 'is_verified' not in data:
            return jsonify({"error": "请提供验证状态"}), 400

        # 更新验证状态
        house.is_verified_by_admin = data['is_verified']
        db.session.commit()

        return jsonify({
            "message": f"房源已{'' if data['is_verified'] else '取消'}验证",
            "house": house_schema.dump(house)
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"验证房源失败: {str(e)}")
        return jsonify({"error": "验证房源失败", "message": str(e)}), 500

@bp.route('/my', methods=['GET'])
@jwt_required()
@role_required('landlord')
def get_my_houses():
    """获取当前房东的房源列表"""
    try:
        current_user_id = get_jwt_identity()

        # 获取查询参数
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 100)

        # 构建查询
        query = House.query.filter_by(landlord_id=current_user_id)

        if status:
            query = query.filter(House.status == status)

        query = query.order_by(desc(House.created_at))

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            "houses": houses_schema.dump(pagination.items),
            "total": pagination.total,
            "pages": pagination.pages,
            "page": page,
            "per_page": per_page
        }), 200

    except Exception as e:
        current_app.logger.error(f"获取我的房源失败: {str(e)}")
        return jsonify({"error": "获取我的房源失败", "message": str(e)}), 500