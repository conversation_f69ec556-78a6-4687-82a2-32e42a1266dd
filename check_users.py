import pymysql

try:
    # 连接到MySQL数据库
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='123456',
        database='smart_rent_dev'
    )
    
    cursor = connection.cursor()
    
    # 查询所有用户
    cursor.execute("SELECT id, username, email, password_hash, role FROM users")
    users = cursor.fetchall()
    
    print("数据库中的用户列表：")
    print("-" * 80)
    for user in users:
        user_id, username, email, password_hash, role = user
        print(f"ID: {user_id}")
        print(f"用户名: {username}")
        print(f"邮箱: {email}")
        print(f"角色: {role}")
        print(f"密码哈希: {password_hash[:50]}..." if len(password_hash) > 50 else f"密码哈希: {password_hash}")
        print(f"密码哈希长度: {len(password_hash)}")
        print(f"是否以$pbkdf2开头: {password_hash.startswith('$pbkdf2')}")
        print("-" * 80)
    
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"数据库查询失败：{e}")
