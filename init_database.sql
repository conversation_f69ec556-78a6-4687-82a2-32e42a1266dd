-- 智能房屋租赁系统数据库初始化脚本
-- 与项目代码模型匹配的表结构

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS smart_rent_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE smart_rent_dev;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(64) NOT NULL UNIQUE,
    email VARCHAR(120) NOT NULL UNIQUE,
    password_hash VARCHAR(128) NOT NULL,
    role VARCHAR(20) NOT NULL COMMENT 'landlord, tenant, admin',
    full_name VARCHAR(64),
    phone_number VARCHAR(20) UNIQUE,
    avatar_url VARCHAR(256),
    registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    mfa_secret VARCHAR(32),
    mfa_enabled BOOLEAN DEFAULT FALSE,
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建房源表
CREATE TABLE IF NOT EXISTS houses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    landlord_id INT NOT NULL,
    address VARCHAR(256) NOT NULL,
    city VARCHAR(64),
    district VARCHAR(64),
    community_name VARCHAR(64),
    house_type VARCHAR(32) COMMENT '公寓, 别墅, 商铺',
    layout VARCHAR(32) COMMENT '2室1厅1卫',
    area FLOAT NOT NULL,
    rent_price DECIMAL(10,2) NOT NULL,
    deposit_price DECIMAL(10,2) NOT NULL,
    decoration_status VARCHAR(32) COMMENT '精装修, 简装修, 毛坯',
    description TEXT,
    photos_urls TEXT COMMENT 'JSON字符串存储图片URL列表',
    video_url VARCHAR(256),
    status VARCHAR(32) DEFAULT '空置' COMMENT '空置, 出租中, 维修中, 已下架',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    latitude FLOAT,
    longitude FLOAT,
    is_verified_by_admin BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (landlord_id) REFERENCES users(id),
    INDEX idx_city (city),
    INDEX idx_status (status),
    INDEX idx_rent_price (rent_price)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建消息表
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    content TEXT NOT NULL,
    sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE,
    conversation_id VARCHAR(64) COMMENT '会话ID，用于分组消息',
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (receiver_id) REFERENCES users(id),
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_conversation (conversation_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建预约看房表
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    house_id INT NOT NULL,
    landlord_id INT NOT NULL,
    requested_time DATETIME NOT NULL,
    message TEXT COMMENT '租客留言',
    status VARCHAR(32) DEFAULT '待确认' COMMENT '待确认, 已确认, 已拒绝, 已取消, 已完成',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES users(id),
    FOREIGN KEY (house_id) REFERENCES houses(id),
    FOREIGN KEY (landlord_id) REFERENCES users(id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建租赁合同表
CREATE TABLE IF NOT EXISTS lease_contracts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    house_id INT NOT NULL,
    tenant_id INT NOT NULL,
    landlord_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    rent_amount_monthly DECIMAL(10,2) NOT NULL,
    deposit_amount DECIMAL(10,2) NOT NULL,
    payment_day_of_month INT NOT NULL,
    contract_terms TEXT,
    contract_document_url VARCHAR(256),
    status VARCHAR(32) DEFAULT '待签署' COMMENT '待签署, 生效中, 已到期, 已解除',
    tenant_signature_timestamp DATETIME,
    landlord_signature_timestamp DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (house_id) REFERENCES houses(id),
    FOREIGN KEY (tenant_id) REFERENCES users(id),
    FOREIGN KEY (landlord_id) REFERENCES users(id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建支付记录表
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lease_contract_id INT NOT NULL,
    tenant_id INT NOT NULL,
    amount_due DECIMAL(10,2) NOT NULL,
    amount_paid DECIMAL(10,2),
    due_date DATE NOT NULL,
    payment_date DATETIME,
    payment_method VARCHAR(32) COMMENT '在线支付, 银行转账',
    transaction_id VARCHAR(64) COMMENT '支付平台交易号',
    status VARCHAR(32) DEFAULT '待支付' COMMENT '待支付, 已支付, 逾期, 部分支付',
    notes TEXT,
    FOREIGN KEY (lease_contract_id) REFERENCES lease_contracts(id),
    FOREIGN KEY (tenant_id) REFERENCES users(id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建维修请求表
CREATE TABLE IF NOT EXISTS repair_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    house_id INT NOT NULL,
    lease_contract_id INT,
    description TEXT NOT NULL,
    photos_urls TEXT COMMENT 'JSON字符串存储图片URL列表',
    status VARCHAR(32) DEFAULT '待处理' COMMENT '待处理, 处理中, 已完成, 已关闭',
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved_at DATETIME,
    handler_notes TEXT,
    FOREIGN KEY (tenant_id) REFERENCES users(id),
    FOREIGN KEY (house_id) REFERENCES houses(id),
    FOREIGN KEY (lease_contract_id) REFERENCES lease_contracts(id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建投诉表
CREATE TABLE IF NOT EXISTS complaints (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reporter_id INT NOT NULL,
    house_id INT,
    related_user_id INT,
    subject VARCHAR(128),
    description TEXT NOT NULL,
    status VARCHAR(32) DEFAULT '待处理' COMMENT '待处理, 处理中, 已解决, 已关闭',
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved_at DATETIME,
    resolver_id INT,
    resolver_notes TEXT,
    FOREIGN KEY (reporter_id) REFERENCES users(id),
    FOREIGN KEY (house_id) REFERENCES houses(id),
    FOREIGN KEY (related_user_id) REFERENCES users(id),
    FOREIGN KEY (resolver_id) REFERENCES users(id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建新闻文章表
CREATE TABLE IF NOT EXISTS news_articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(128) NOT NULL,
    content TEXT NOT NULL,
    author_id INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_published BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (author_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;