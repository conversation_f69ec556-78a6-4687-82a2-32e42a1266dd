import requests
import json

# API基础URL
BASE_URL = 'http://127.0.0.1:5000/api'

def test_register():
    """测试用户注册"""
    url = f'{BASE_URL}/auth/register'
    data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "role": "tenant",
        "full_name": "测试用户",
        "phone_number": "13800138000"
    }
    
    response = requests.post(url, json=data)
    print(f"注册响应状态码: {response.status_code}")
    print(f"注册响应内容: {response.text}")
    return response.status_code == 201

def test_login():
    """测试用户登录"""
    url = f'{BASE_URL}/auth/login'
    data = {
        "username": "testuser",
        "password": "testpassword123"
    }
    
    response = requests.post(url, json=data)
    print(f"登录响应状态码: {response.status_code}")
    print(f"登录响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        return result.get('access_token')
    return None

def test_wrong_password():
    """测试错误密码"""
    url = f'{BASE_URL}/auth/login'
    data = {
        "username": "testuser",
        "password": "wrongpassword"
    }
    
    response = requests.post(url, json=data)
    print(f"错误密码登录状态码: {response.status_code}")
    print(f"错误密码登录响应: {response.text}")
    return response.status_code == 401

if __name__ == "__main__":
    print("=== 测试用户认证功能 ===")
    
    # 测试注册
    print("\n1. 测试用户注册:")
    register_success = test_register()
    
    if register_success:
        print("✅ 注册成功")
        
        # 测试正确密码登录
        print("\n2. 测试正确密码登录:")
        token = test_login()
        if token:
            print("✅ 登录成功，获得token")
        else:
            print("❌ 登录失败")
        
        # 测试错误密码登录
        print("\n3. 测试错误密码登录:")
        wrong_login = test_wrong_password()
        if wrong_login:
            print("✅ 错误密码正确被拒绝")
        else:
            print("❌ 错误密码验证有问题")
    else:
        print("❌ 注册失败，跳过登录测试")
